#!/usr/bin/env python3
"""
测试新添加的QingYun模型是否正确显示
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.llm.models import load_models_from_json

def test_model_display():
    """测试模型配置加载和显示"""
    print("🔍 测试新添加的QingYun模型配置")
    print("=" * 60)
    
    # 加载模型配置
    try:
        models = load_models_from_json("src/llm/api_models.json")
        print(f"✅ 成功加载 {len(models)} 个模型配置")
    except Exception as e:
        print(f"❌ 加载模型配置失败: {e}")
        return False
    
    # 查找新添加的模型
    new_models = [
        "claude-3-haiku-20240307",
        "ERNIE-Speed-128K", 
        "qwen3-1.7b",
        "glm-4-flash",
        "deepseek-v3",
        "llama-2-70b"
    ]
    
    print(f"\n📋 检查新添加的 {len(new_models)} 个模型:")
    print("-" * 40)
    
    found_models = []
    missing_models = []
    
    for model_name in new_models:
        found = False
        for model in models:
            if model.model_name == model_name and model.provider.value == "QingYun":
                found_models.append(model)
                print(f"✅ {model.display_name}")
                found = True
                break
        
        if not found:
            missing_models.append(model_name)
            print(f"❌ {model_name} - 未找到")
    
    print(f"\n📊 统计结果:")
    print(f"✅ 找到的模型: {len(found_models)}")
    print(f"❌ 缺失的模型: {len(missing_models)}")
    
    if missing_models:
        print(f"\n⚠️  缺失的模型列表:")
        for model in missing_models:
            print(f"   - {model}")
    
    # 显示所有QingYun模型
    print(f"\n🌐 所有QingYun模型列表:")
    print("-" * 40)
    qingyun_models = [m for m in models if m.provider.value == "QingYun"]
    for i, model in enumerate(qingyun_models, 1):
        print(f"{i:2d}. {model.display_name}")
    
    return len(missing_models) == 0

if __name__ == "__main__":
    success = test_model_display()
    if success:
        print(f"\n🎉 所有新模型配置验证成功！")
    else:
        print(f"\n⚠️  部分模型配置存在问题，请检查配置文件。")

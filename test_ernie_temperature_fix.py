#!/usr/bin/env python3
"""
测试ERNIE模型temperature参数修复
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.llm.models import get_model, ModelProvider

# 加载环境变量
load_dotenv()

def test_ernie_temperature():
    """测试ERNIE模型temperature参数修复"""
    print("🧪 测试ERNIE模型temperature参数修复")
    print("-" * 50)
    
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ QINGYUN_API_KEY环境变量未设置")
        return False
    
    try:
        # 获取ERNIE模型
        model_name = "ERNIE-Speed-128K"
        print(f"🔄 获取模型: {model_name}")
        
        llm = get_model(model_name, ModelProvider.QINGYUN)
        print(f"✅ 模型获取成功")
        
        # 测试简单调用
        test_prompt = "请简单介绍一下人工智能。"
        print(f"🔄 测试调用: {test_prompt}")
        
        response = llm.invoke(test_prompt)
        response_text = response.content if hasattr(response, 'content') else str(response)
        
        print(f"✅ 调用成功")
        print(f"📝 响应长度: {len(response_text)} 字符")
        print(f"📄 响应预览: {response_text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_ernie_temperature()
    if success:
        print(f"\n🎉 ERNIE模型temperature参数修复成功！")
    else:
        print(f"\n⚠️  ERNIE模型仍存在问题，需要进一步调试。")

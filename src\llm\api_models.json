[{"display_name": "[q<PERSON><PERSON>] claude-3.5-sonnet", "model_name": "claude-3-5-sonnet-latest", "provider": "QingYun"}, {"display_name": "[qingyun] gemini-2.0-flash", "model_name": "gemini-2.0-flash", "provider": "QingYun"}, {"display_name": "[q<PERSON><PERSON>] gemini-2.5-pro", "model_name": "gemini-2.5-pro-exp-03-25", "provider": "QingYun"}, {"display_name": "[qingyun] gpt-4.5", "model_name": "gpt-4.5-preview", "provider": "QingYun"}, {"display_name": "[qingyun] gpt-4o", "model_name": "gpt-4o", "provider": "QingYun"}, {"display_name": "[qingyun] gpt-3.5-turbo", "model_name": "gpt-3.5-turbo", "provider": "QingYun"}, {"display_name": "[qingyun] o3", "model_name": "o3", "provider": "QingYun"}, {"display_name": "[qingyun] o4-mini", "model_name": "o4-mini", "provider": "QingYun"}, {"display_name": "[qingyun] meta-llama/llama-4-scout", "model_name": "meta-llama/llama-4-scout", "provider": "QingYun"}, {"display_name": "[qingyun] meta-llama/llama-4-maverick", "model_name": "meta-llama/llama-4-maverick", "provider": "QingYun"}, {"display_name": "[qing<PERSON>] grok-beta", "model_name": "grok-beta", "provider": "QingYun"}, {"display_name": "[q<PERSON><PERSON>] grok-3-reasoner", "model_name": "grok-3-reasoner", "provider": "QingYun"}, {"display_name": "[qingyun] grok-2-1212", "model_name": "grok-2-1212", "provider": "QingYun"}, {"display_name": "[qingyun] llama-3-sonar-large-32k-chat", "model_name": "llama-3-sonar-large-32k-chat", "provider": "QingYun"}, {"display_name": "[qingyun] glm-4-flash", "model_name": "glm-4-flash", "provider": "QingYun"}, {"display_name": "[q<PERSON><PERSON>] llama-2-70b", "model_name": "llama-2-70b", "provider": "QingYun"}, {"display_name": "[q<PERSON><PERSON>] claude-3-haiku-20240307", "model_name": "claude-3-haiku-20240307", "provider": "QingYun"}, {"display_name": "[qingyun] ERNIE-Speed-128K", "model_name": "ERNIE-Speed-128K", "provider": "QingYun"}, {"display_name": "[q<PERSON><PERSON>] qwen3-1.7b", "model_name": "qwen3-1.7b", "provider": "QingYun"}, {"display_name": "[q<PERSON><PERSON>] deepseek-v3", "model_name": "deepseek-v3", "provider": "QingYun"}, {"display_name": "[qingyun] custom", "model_name": "-", "provider": "QingYun"}]